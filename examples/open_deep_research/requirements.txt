anthropic>=0.37.1
audioop-lts<1.0; python_version >= "3.13" # required to use pydub in Python >=3.13; LTS port of the removed Python builtin module audioop
beautifulsoup4>=4.12.3
datasets>=2.21.0
google_search_results>=2.4.2
huggingface_hub>=0.23.4
mammoth>=1.8.0
markdownify>=0.13.1
numexpr>=2.10.1
numpy>=2.1.2
openai>=1.52.2
openpyxl
pandas>=2.2.3
pathvalidate>=3.2.1
pdfminer>=20191125
pdfminer.six>=20240706
Pillow>=11.0.0
puremagic>=1.28
pypdf>=5.1.0
python-dotenv>=1.0.1
python_pptx>=1.0.2
Requests>=2.32.3
tqdm>=4.66.4
torch>=2.2.2
torchvision>=0.17.2
transformers>=4.46.0
youtube_transcript_api>=0.6.2
chess
sympy
pubchempy
Bio
scikit-learn
scipy
pydub
PyPDF2
python-pptx
torch
xlrd
SpeechRecognition
