name: Python tests

on:
  pull_request:
  push:
    branches:
      - ci-*

env:
  UV_SYSTEM_PYTHON: 1

jobs:
  build-ubuntu:
    runs-on: ubuntu-latest
    env:
      UV_HTTP_TIMEOUT: 600 # max 10min to install deps

    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.12"]

    steps:
      - uses: actions/checkout@v2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}

      # Setup venv
      - name: Setup uv
        run: |
          pip install --upgrade uv

      # Install dependencies
      - name: Install dependencies
        run: |
          uv pip install "smolagents[test] @ ."

      # Run tests
      - name: Test with pytest
        run: |
          pytest ./tests/
